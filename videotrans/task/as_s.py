from typing import List, Optional
from dataclasses import dataclass


@dataclass
class Subtitle:
    """Subtitle data structure"""
    start_time: str
    end_time: str
    text: str


@dataclass
class SubtitleStyle:
    """Subtitle style configuration"""
    font_family: str
    font_size: int
    primary_color: str
    primary_stroke_width: int
    primary_stroke_color: str
    primary_margin_v: int
    primary_background_color: str
    show_primary_background: bool
    secondary_background_color: str
    secondary_color: str
    secondary_font_family: str
    secondary_font_size: int
    secondary_stroke_color: str
    secondary_stroke_width: int
    secondary_margin_v: int
    shadow_color: str
    show_primary_shadow: bool
    show_primary_stroke: bool
    show_secondary_background: bool
    show_secondary_shadow: bool
    show_secondary_stroke: bool


@dataclass
class VideoFormat:
    """Video format configuration"""
    width: Optional[int] = None
    height: Optional[int] = None


def convert_time_to_ass(time: str) -> str:
    """
    Convert time format from "00:00:00.000" to "0:00:00.00" (ASS format)

    Args:
        time: Time string in format "00:00:00.000"

    Returns:
        Time string in ASS format "0:00:00.00"
    """
    # Remove the last millisecond digit
    time_without_last_ms = time[:-1]
    # Remove leading zero if present
    if time_without_last_ms.startswith('0'):
        return time_without_last_ms[1:]
    return time_without_last_ms


def convert_color_to_ass(color: str) -> str:
    """
    Convert color from "#RRGGBB", "#RRGGBBAA", or "&HAABBGGRR" to ASS format "&HAABBGGRR"

    Args:
        color: Color string in hex format "#RRGGBB", "#RRGGBBAA", or "&HAABBGGRR"

    Returns:
        Color string in ASS format "&HAABBGGRR"
    """
    if not color:
        return "&H00FFFFFF"

    # If already in ASS format, return as is
    if color.startswith("&H") and len(color) in [8, 10]:
        return color

    # Remove # symbol
    hex_color = color.replace("#", "").replace("&H", "")

    # Handle different formats
    if len(hex_color) == 6:
        # RRGGBB format - add full opacity alpha
        rgba = f"00{hex_color}"
    elif len(hex_color) == 8:
        # AARRGGBB or RRGGBBAA format - need to determine which
        # If it starts with 00-FF and looks like alpha, treat as AARRGGBB
        # Otherwise treat as RRGGBBAA
        rgba = hex_color
    else:
        # Invalid format, return default white
        return "&H00FFFFFF"

    # Extract components - assume AARRGGBB format
    aa = rgba[0:2]
    rr = rgba[2:4]
    gg = rgba[4:6]
    bb = rgba[6:8]

    # Return ASS format: &HAABBGGRR
    return f"&H{aa}{bb}{gg}{rr}"


def darken_ass_color(ass_color: str, factor: float = 0.7) -> str:
    """
    Darken an ASS color by a factor for better video rendering

    Args:
        ass_color: ASS color in format "&HAABBGGRR"
        factor: Darkening factor (0.0 = black, 1.0 = original)

    Returns:
        Darkened ASS color
    """
    if not ass_color.startswith("&H") or len(ass_color) != 10:
        return ass_color

    hex_part = ass_color[2:]  # Remove &H
    aa = hex_part[0:2]
    bb = hex_part[2:4]
    gg = hex_part[4:6]
    rr = hex_part[6:8]

    # Convert to RGB, darken, convert back
    r = int(int(rr, 16) * factor)
    g = int(int(gg, 16) * factor)
    b = int(int(bb, 16) * factor)
    a = int(aa, 16)

    # Clamp values
    r = max(0, min(255, r))
    g = max(0, min(255, g))
    b = max(0, min(255, b))

    return f"&H{a:02X}{b:02X}{g:02X}{r:02X}"


def generate_ass_styles(style: SubtitleStyle, is_secondary: bool = False) -> str:
    """
    Generate ASS style definition

    Args:
        style: SubtitleStyle configuration
        is_secondary: Whether to generate secondary style

    Returns:
        ASS style definition string
    """
    font_family = style.secondary_font_family if is_secondary else style.font_family
    font_size = style.secondary_font_size if is_secondary else style.font_size

    # Use appropriate colors based on style type
    if is_secondary:
        text_color = convert_color_to_ass(style.secondary_color)
        stroke_color = convert_color_to_ass(style.secondary_stroke_color)
        background_color = convert_color_to_ass(style.secondary_background_color) if style.show_secondary_background else "&H00000000"
        outline_width = style.secondary_stroke_width if style.show_secondary_stroke else 0
        show_shadow = style.show_secondary_shadow
        margin_v = style.secondary_margin_v
        show_background = style.show_secondary_background
    else:
        text_color = convert_color_to_ass(style.primary_color)
        stroke_color = convert_color_to_ass(style.primary_stroke_color)
        background_color = convert_color_to_ass(style.primary_background_color) if style.show_primary_background else "&H00000000"
        outline_width = style.primary_stroke_width if style.show_primary_stroke else 0
        show_shadow = style.show_primary_shadow
        margin_v = style.primary_margin_v
        show_background = style.show_primary_background

    style_name = "Secondary" if is_secondary else "Default"
    shadow_value = 2 if show_shadow else 0

    # For backgrounds to work properly in ASS, we need specific settings:
    # 1. BorderStyle must be 3 (opaque box) or 4 (opaque box with outline)
    # 2. For BorderStyle 3, we need a minimum outline width (even if stroke is disabled)
    # 3. Background color must have proper alpha channel

    if show_background:
        # Only modify background color if it's fully transparent black
        if background_color == "&H00000000":
            background_color = "&H80000000"  # Semi-transparent black
        # Don't modify other colors - trust the user's alpha settings

        # For backgrounds to show, we need BorderStyle 3 and minimum outline
        border_style = 3  # Opaque box
        # Ensure minimum outline for background visibility (required by many ASS renderers)
        if outline_width == 0:
            outline_width = 1  # Minimum outline required for background
    else:
        border_style = 1  # Normal outline/shadow

    # ASS Style format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour,
    # Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow,
    # Alignment, MarginL, MarginR, MarginV, Encoding
    return (f"Style: {style_name},{font_family},{font_size},{text_color},"
            f"{text_color},{stroke_color},{background_color},0,0,0,0,100,100,0,0,"
            f"{border_style},{outline_width},{shadow_value},2,10,10,{margin_v},1")


def generate_bilingual_ass(
        src_subtitles: List[Subtitle],
        trans_subtitles: List[Subtitle],
        style: SubtitleStyle,
        format_config: Optional[VideoFormat] = None
) -> str:
    """
    Generate bilingual ASS format subtitles

    Args:
        src_subtitles: List of source language subtitles
        trans_subtitles: List of translated subtitles
        style: SubtitleStyle configuration
        format_config: Optional VideoFormat configuration

    Returns:
        Complete ASS subtitle file content as string
    """
    # Set default video dimensions if not provided
    width = format_config.width if format_config and format_config.width else 1920
    height = format_config.height if format_config and format_config.height else 1080

    # Generate header
    header = f"""[Script Info]
ScriptType: v4.00+
PlayResX: {width}
PlayResY: {height}
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
{generate_ass_styles(style)}
{generate_ass_styles(style, True)}

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text"""

    # Generate events
    events = []
    for index, src_sub in enumerate(src_subtitles):
        trans_sub = trans_subtitles[index] if index < len(trans_subtitles) else None
        start = convert_time_to_ass(src_sub.start_time)
        end = convert_time_to_ass(src_sub.end_time)

        # Source language subtitle on top (using primary marginV)
        src_line = f"Dialogue: 0,{start},{end},Default,,0,0,{style.primary_margin_v},,{src_sub.text}"

        # Translation subtitle below (using secondary marginV)
        trans_line = ""
        if trans_sub:
            trans_line = f"Dialogue: 0,{start},{end},Secondary,,0,0,{style.secondary_margin_v},,{trans_sub.text}"

        # Combine lines, filtering out empty ones
        lines = [line for line in [src_line, trans_line] if line]
        events.append("\n".join(lines))

    events_text = "\n".join(events)

    return f"{header}\n\n{events_text}"


# Example usage
if __name__ == "__main__":
    # Example subtitle data
    src_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Hello world"),
        Subtitle("00:00:04.000", "00:00:06.000", "How are you?")
    ]

    trans_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Hola mundo"),
        Subtitle("00:00:04.000", "00:00:06.000", "¿Cómo estás?")
    ]

    # Example style configuration
    style = SubtitleStyle(
        font_family="Arial",
        font_size=20,
        primary_color="#FFFFFF",
        primary_stroke_width=2,
        primary_margin_v=40,  # Source subtitle vertical margin
        primary_background_color="#33000000",  # Semi-transparent black background
        show_primary_background=False,  # Don't show primary background by default
        secondary_background_color="#000000",
        secondary_color="#FFFF00",
        secondary_font_family="Arial",
        secondary_font_size=18,
        secondary_stroke_color="#000000",
        secondary_stroke_width=1,
        secondary_margin_v=0,  # Translation subtitle vertical margin
        shadow_color="#000000",
        show_primary_shadow=True,
        show_primary_stroke=True,
        show_secondary_background=True,  # Show secondary background
        show_secondary_shadow=True,
        show_secondary_stroke=True
    )

    # Generate bilingual ASS
    ass_content = generate_bilingual_ass(src_subs, trans_subs, style)
    print("=== Example with background colors disabled ===")
    print(ass_content)

    # Test with background colors enabled
    style_with_bg = SubtitleStyle(
        font_family="Arial",
        font_size=20,
        primary_color="#FFFFFF",
        primary_stroke_width=2,
        primary_margin_v=40,
        primary_background_color="#80000000",  # Semi-transparent black
        show_primary_background=True,  # Enable primary background
        secondary_background_color="#80FF0000",  # Semi-transparent red
        secondary_color="#FFFF00",
        secondary_font_family="Arial",
        secondary_font_size=18,
        secondary_stroke_color="#000000",
        secondary_stroke_width=1,
        secondary_margin_v=0,
        shadow_color="#000000",
        show_primary_shadow=True,
        show_primary_stroke=True,
        show_secondary_background=True,  # Enable secondary background
        show_secondary_shadow=True,
        show_secondary_stroke=True
    )

    ass_content_with_bg = generate_bilingual_ass(src_subs, trans_subs, style_with_bg)
    print("\n=== Example with background colors enabled ===")
    print(ass_content_with_bg)
